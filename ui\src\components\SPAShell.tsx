/**
 * SPA Shell Component
 * Main container for the modular SPA functionality
 */

import React, { useEffect, useRef } from 'react';
import { Router } from '../modules/spa/core/Router';
import { PatchManager } from '../modules/spa/core/PatchManager';
import { ComponentRegistry } from '../modules/spa/registries/ComponentRegistry';
import { ActionRegistry } from '../modules/spa/registries/ActionRegistry';

interface SPAShellProps {
  className?: string;
  enableEditMode?: boolean;
}

export const SPAShell: React.FC<SPAShellProps> = ({ 
  className = '', 
  enableEditMode = false 
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const spaCore = useRef<any>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // Initialize SPA Core System
    const componentRegistry = new ComponentRegistry();
    const actionRegistry = new ActionRegistry();
    const router = new Router();
    const patchManager = new PatchManager();

    // Store in ref for cleanup
    spaCore.current = {
      componentRegistry,
      actionRegistry,
      router,
      patchManager,
      isEditMode: enableEditMode
    };

    // Make globally available for compatibility
    (window as any).spaCore = spaCore.current;

    // Initialize default views
    initializeDefaultViews(router);

    // Setup event listeners
    setupEventListeners(containerRef.current, spaCore.current);

    // Navigate to default view
    router.navigateToView('dashboard');

    console.log('🚀 SPA Shell initialized');

    // Cleanup
    return () => {
      componentRegistry.clear();
      actionRegistry.clear();
      delete (window as any).spaCore;
      console.log('🧹 SPA Shell cleaned up');
    };
  }, [enableEditMode]);

  return (
    <div ref={containerRef} className={`spa-shell ${className}`}>
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex">
              <div className="flex-shrink-0 flex items-center">
                <h1 className="text-xl font-semibold text-gray-900">
                  SPA Dashboard
                </h1>
              </div>
              <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                <div id="navItems" className="flex space-x-4">
                  {/* Navigation items will be populated by router */}
                </div>
              </div>
            </div>
            <div className="flex items-center">
              {enableEditMode && (
                <button
                  id="editModeBtn"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                >
                  Edit Mode
                </button>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="flex-1">
        <div id="viewContainer" className="h-full">
          {/* View content will be populated by router */}
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-500">Loading...</p>
            </div>
          </div>
        </div>
      </main>

      {/* Diff Modal */}
      <div id="diffModal" className="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
          <div className="mt-3">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Review Changes
            </h3>
            <div id="diffContent" className="mt-2 max-h-96 overflow-y-auto bg-gray-50 p-4 rounded border text-sm">
              {/* Diff content will be populated */}
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                id="rejectDiff"
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
              >
                Reject
              </button>
              <button
                id="applyDiff"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Apply Changes
              </button>
            </div>
          </div>
          <button
            id="closeDiffModal"
            className="absolute top-3 right-3 text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {/* Loading Overlay */}
      <div id="loadingOverlay" className="hidden fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-40">
        <div className="bg-white rounded-lg p-6 shadow-xl">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-gray-700">Processing changes...</span>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Initialize default views
 */
function initializeDefaultViews(router: Router): void {
  router.addView('dashboard', getDashboardContent(), 'Dashboard');
  router.addView('analytics', getAnalyticsContent(), 'Analytics');
  router.addView('settings', getSettingsContent(), 'Settings');
  router.updateNavigation();
}

/**
 * Setup event listeners
 */
function setupEventListeners(container: HTMLElement, spaCore: any): void {
  // Navigation click delegation
  container.addEventListener('click', (e) => {
    const target = e.target as HTMLElement;
    const navElement = target.closest('[data-nav]');
    if (navElement) {
      e.preventDefault();
      const viewName = navElement.getAttribute('data-nav');
      if (viewName) {
        spaCore.router.navigateToView(viewName);
      }
    }
  });

  // Action click delegation
  container.addEventListener('click', (e) => {
    const target = e.target as HTMLElement;
    const actionElement = target.closest('[data-action]');
    if (actionElement) {
      e.preventDefault();
      const action = actionElement.getAttribute('data-action');
      const actionTarget = actionElement.getAttribute('data-target');
      const params = extractDataParams(actionElement as HTMLElement);
      
      if (action) {
        spaCore.actionRegistry.execute(action, actionTarget, params, actionElement);
      }
    }
  });

  // Edit mode toggle
  const editModeBtn = container.querySelector('#editModeBtn');
  if (editModeBtn) {
    editModeBtn.addEventListener('click', () => {
      toggleEditMode(spaCore);
    });
  }

  // Diff modal controls
  const closeDiffModal = container.querySelector('#closeDiffModal');
  const applyDiff = container.querySelector('#applyDiff');
  const rejectDiff = container.querySelector('#rejectDiff');

  if (closeDiffModal) {
    closeDiffModal.addEventListener('click', () => {
      spaCore.patchManager.hideDiffModal();
    });
  }

  if (applyDiff) {
    applyDiff.addEventListener('click', () => {
      spaCore.patchManager.applyPendingDiff();
    });
  }

  if (rejectDiff) {
    rejectDiff.addEventListener('click', () => {
      spaCore.patchManager.rejectPendingDiff();
    });
  }

  // Router callbacks for component reinitialization
  spaCore.router.onAfterNavigate((viewName: string) => {
    const viewContainer = container.querySelector('#viewContainer');
    if (viewContainer) {
      spaCore.componentRegistry.reinitialize(viewContainer as HTMLElement);
    }
  });
}

/**
 * Toggle edit mode
 */
function toggleEditMode(spaCore: any): void {
  spaCore.isEditMode = !spaCore.isEditMode;
  const btn = document.getElementById('editModeBtn');
  
  if (btn) {
    if (spaCore.isEditMode) {
      btn.textContent = 'Exit Edit';
      btn.classList.add('bg-red-600', 'hover:bg-red-700');
      btn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
      enableEditMode(spaCore);
    } else {
      btn.textContent = 'Edit Mode';
      btn.classList.remove('bg-red-600', 'hover:bg-red-700');
      btn.classList.add('bg-blue-600', 'hover:bg-blue-700');
      disableEditMode();
    }
  }
  
  console.log(`${spaCore.isEditMode ? '✏️ Enabled' : '🔒 Disabled'} edit mode`);
}

/**
 * Enable edit mode
 */
function enableEditMode(spaCore: any): void {
  document.body.classList.add('edit-mode');
  
  const handleEditClick = (e: Event) => {
    if (!spaCore.isEditMode) return;
    
    const element = e.target as HTMLElement;
    const section = element.closest('[data-view]');
    
    if (section) {
      e.preventDefault();
      e.stopPropagation();
      
      const viewName = section.getAttribute('data-view');
      const prompt = `Improve the clicked element: ${element.textContent?.trim() || element.tagName}`;
      
      if (viewName) {
        const currentContent = section.innerHTML;
        spaCore.patchManager.applyLlmPatchToView(viewName, currentContent, prompt);
      }
    }
  };
  
  document.addEventListener('click', handleEditClick);
  (spaCore as any).editClickHandler = handleEditClick;
}

/**
 * Disable edit mode
 */
function disableEditMode(): void {
  document.body.classList.remove('edit-mode');
  
  const editClickHandler = (window as any).spaCore?.editClickHandler;
  if (editClickHandler) {
    document.removeEventListener('click', editClickHandler);
    delete (window as any).spaCore.editClickHandler;
  }
}

/**
 * Extract data parameters from element
 */
function extractDataParams(element: HTMLElement): any {
  const params: any = {};
  
  Array.from(element.attributes).forEach(attr => {
    if (attr.name.startsWith('data-') && 
        !['data-action', 'data-target', 'data-nav', 'data-view', 'data-component'].includes(attr.name)) {
      const key = attr.name.replace('data-', '').replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
      
      try {
        params[key] = JSON.parse(attr.value);
      } catch {
        params[key] = attr.value;
      }
    }
  });
  
  return params;
}

/**
 * Default view content templates
 */
function getDashboardContent(): string {
  return `
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Dashboard</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
                    <dd class="text-lg font-medium text-gray-900">$71,897</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
          
          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">New Users</dt>
                    <dd class="text-lg font-medium text-gray-900">2,347</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
          
          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">Orders</dt>
                    <dd class="text-lg font-medium text-gray-900">1,423</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="bg-white shadow rounded-lg">
          <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
            <div data-component="grid" data-url="/api/dashboard/activity" class="min-h-[200px]">
              <div class="animate-pulse">
                <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div class="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div class="h-4 bg-gray-200 rounded w-2/3"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

function getAnalyticsContent(): string {
  return `
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Analytics</h1>
        
        <div class="bg-white shadow rounded-lg p-6 mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Revenue Chart</h3>
          <div data-component="chart" data-type="line" data-url="/api/analytics/revenue" class="h-64">
            <div class="h-full bg-gray-100 rounded flex items-center justify-center">
              <span class="text-gray-500">Loading chart...</span>
            </div>
          </div>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">User Engagement</h3>
            <div data-component="chart" data-type="doughnut" data-url="/api/analytics/engagement" class="h-48">
              <div class="h-full bg-gray-100 rounded flex items-center justify-center">
                <span class="text-gray-500">Loading chart...</span>
              </div>
            </div>
          </div>
          
          <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Performance Metrics</h3>
            <div class="space-y-4">
              <div class="flex justify-between">
                <span class="text-sm font-medium text-gray-500">Page Load Time</span>
                <span class="text-sm text-gray-900">1.2s</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm font-medium text-gray-500">Bounce Rate</span>
                <span class="text-sm text-gray-900">34%</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm font-medium text-gray-500">Conversion Rate</span>
                <span class="text-sm text-gray-900">2.4%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

function getSettingsContent(): string {
  return `
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Settings</h1>
        
        <div class="space-y-6">
          <div class="bg-white shadow rounded-lg">
            <div class="p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Profile Settings</h3>
              <form class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700">Name</label>
                  <input type="text" value="John Doe" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Email</label>
                  <input type="email" value="<EMAIL>" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                  <button 
                    type="button" 
                    data-action="showNotification" 
                    data-message="Profile saved successfully!" 
                    data-type="success"
                    class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    Save Changes
                  </button>
                </div>
              </form>
            </div>
          </div>
          
          <div class="bg-white shadow rounded-lg">
            <div class="p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Preferences</h3>
              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <div>
                    <h4 class="text-sm font-medium text-gray-900">Email Notifications</h4>
                    <p class="text-sm text-gray-500">Receive email updates about your account</p>
                  </div>
                  <button 
                    type="button" 
                    data-action="toggleClass"
                    data-class="bg-blue-600"
                    class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 bg-blue-600"
                  >
                    <span class="translate-x-5 inline-block h-5 w-5 transform rounded-full bg-white transition duration-200 ease-in-out"></span>
                  </button>
                </div>
                
                <div class="flex items-center justify-between">
                  <div>
                    <h4 class="text-sm font-medium text-gray-900">Dark Mode</h4>
                    <p class="text-sm text-gray-500">Switch to dark theme</p>
                  </div>
                  <button 
                    type="button" 
                    data-action="toggleClass"
                    data-class="bg-blue-600"
                    class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 bg-gray-200"
                  >
                    <span class="translate-x-0 inline-block h-5 w-5 transform rounded-full bg-white transition duration-200 ease-in-out"></span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

export default SPAShell;

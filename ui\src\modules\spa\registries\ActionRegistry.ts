/**
 * Action Registry
 * Manages action handlers for UI interactions
 */

type ActionHandler = (target: string | null, params: any, element: HTMLElement) => Promise<void> | void;

export class ActionRegistry {
  private actions = new Map<string, ActionHandler>();

  constructor() {
    this.registerDefaultActions();
  }

  /**
   * Register an action handler
   */
  register(name: string, handler: ActionHandler): void {
    this.actions.set(name, handler);
    console.log(`⚡ Action registered: ${name}`);
  }

  /**
   * Execute an action
   */
  async execute(action: string, target: string | null, params: any, element: HTMLElement): Promise<boolean> {
    const handler = this.actions.get(action);
    if (!handler) {
      console.warn(`⚠️ Action '${action}' not registered`);
      return false;
    }

    try {
      console.log(`🎬 Executing action: ${action}`, { target, params });
      await handler(target, params, element);
      return true;
    } catch (error) {
      console.error(`❌ Error executing action '${action}':`, error);
      return false;
    }
  }

  /**
   * Check if action is registered
   */
  isRegistered(action: string): boolean {
    return this.actions.has(action);
  }

  /**
   * Get all registered actions
   */
  getRegisteredActions(): string[] {
    return Array.from(this.actions.keys());
  }

  /**
   * Unregister an action
   */
  unregister(name: string): boolean {
    const removed = this.actions.delete(name);
    if (removed) {
      console.log(`🗑️ Action unregistered: ${name}`);
    }
    return removed;
  }

  /**
   * Register default actions
   */
  private registerDefaultActions(): void {
    // Modal actions
    this.register('openModal', async (target, params, element) => {
      if (!target) {
        console.warn('Modal target not specified');
        return;
      }

      const modal = document.getElementById(target);
      if (modal) {
        modal.classList.remove('hidden');
        console.log(`📖 Modal opened: ${target}`);
      } else {
        console.warn(`Modal not found: ${target}`);
      }
    });

    this.register('closeModal', async (target, params, element) => {
      // Find closest modal if no target specified
      const modal = target ? document.getElementById(target) : element.closest('.modal');
      if (modal) {
        modal.classList.add('hidden');
        console.log(`📕 Modal closed: ${target || 'closest'}`);
      }
    });

    // Navigation actions
    this.register('navigate', async (target, params, element) => {
      if (!target) {
        console.warn('Navigation target not specified');
        return;
      }

      // Trigger navigation via global SPA core
      if ((window as any).spaCore && (window as any).spaCore.router) {
        await (window as any).spaCore.router.navigateToView(target);
      } else {
        console.warn('SPA core not available for navigation');
      }
    });

    // Toggle actions
    this.register('toggle', async (target, params, element) => {
      const targetElement = target ? document.getElementById(target) : element;
      if (targetElement) {
        targetElement.classList.toggle('hidden');
        console.log(`🔄 Toggled visibility: ${target || 'self'}`);
      }
    });

    this.register('toggleClass', async (target, params, element) => {
      const targetElement = target ? document.getElementById(target) : element;
      const className = params.class || params.className;
      
      if (targetElement && className) {
        targetElement.classList.toggle(className);
        console.log(`🎨 Toggled class '${className}' on: ${target || 'self'}`);
      }
    });

    // Form actions
    this.register('submitForm', async (target, params, element) => {
      const form = target ? document.getElementById(target) as HTMLFormElement : 
                   element.closest('form') as HTMLFormElement;
      
      if (form) {
        // Create custom submit event
        const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
        form.dispatchEvent(submitEvent);
        console.log(`📤 Form submitted: ${target || 'closest'}`);
      }
    });

    this.register('resetForm', async (target, params, element) => {
      const form = target ? document.getElementById(target) as HTMLFormElement : 
                   element.closest('form') as HTMLFormElement;
      
      if (form) {
        form.reset();
        console.log(`🔄 Form reset: ${target || 'closest'}`);
      }
    });

    // Content actions
    this.register('loadContent', async (target, params, element) => {
      const url = params.url;
      const targetElement = target ? document.getElementById(target) : element;
      
      if (!url || !targetElement) {
        console.warn('URL or target element missing for loadContent');
        return;
      }

      try {
        const response = await fetch(url);
        const content = await response.text();
        targetElement.innerHTML = content;
        console.log(`📥 Content loaded from ${url} into: ${target || 'self'}`);
      } catch (error) {
        console.error('Failed to load content:', error);
        targetElement.innerHTML = '<p class="text-red-500">Failed to load content</p>';
      }
    });

    // Notification actions
    this.register('showNotification', async (target, params, element) => {
      const message = params.message || 'Notification';
      const type = params.type || 'info';
      const duration = parseInt(params.duration) || 3000;

      this.showNotification(message, type, duration);
    });

    // Copy to clipboard
    this.register('copyToClipboard', async (target, params, element) => {
      const text = params.text || element.textContent || '';
      
      try {
        await navigator.clipboard.writeText(text);
        this.showNotification('Copied to clipboard!', 'success', 2000);
        console.log(`📋 Copied to clipboard: ${text.substring(0, 50)}...`);
      } catch (error) {
        console.error('Failed to copy to clipboard:', error);
        this.showNotification('Failed to copy to clipboard', 'error', 3000);
      }
    });

    // Print action
    this.register('print', async (target, params, element) => {
      const targetElement = target ? document.getElementById(target) : null;
      
      if (targetElement) {
        // Print specific element
        const printWindow = window.open('', '_blank');
        if (printWindow) {
          printWindow.document.write(`
            <html>
              <head>
                <title>Print</title>
                <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
              </head>
              <body>${targetElement.outerHTML}</body>
            </html>
          `);
          printWindow.document.close();
          printWindow.print();
        }
      } else {
        // Print entire page
        window.print();
      }
      
      console.log(`🖨️ Print triggered for: ${target || 'page'}`);
    });

    // Download action
    this.register('download', async (target, params, element) => {
      const url = params.url;
      const filename = params.filename || 'download';
      
      if (url) {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        console.log(`💾 Download triggered: ${filename}`);
      }
    });

    // Scroll actions
    this.register('scrollTo', async (target, params, element) => {
      const targetElement = target ? document.getElementById(target) : element;
      
      if (targetElement) {
        targetElement.scrollIntoView({ 
          behavior: 'smooth',
          block: params.block || 'start',
          inline: params.inline || 'nearest'
        });
        console.log(`📜 Scrolled to: ${target || 'self'}`);
      }
    });

    this.register('scrollToTop', async (target, params, element) => {
      window.scrollTo({ top: 0, behavior: 'smooth' });
      console.log('📜 Scrolled to top');
    });

    console.log('✅ Default actions registered');
  }

  /**
   * Show notification helper
   */
  private showNotification(message: string, type: string = 'info', duration: number = 3000): void {
    const notification = document.createElement('div');
    
    const typeClasses = {
      info: 'bg-blue-500',
      success: 'bg-green-500',
      warning: 'bg-yellow-500',
      error: 'bg-red-500'
    };
    
    const bgClass = typeClasses[type as keyof typeof typeClasses] || typeClasses.info;
    
    notification.className = `fixed top-4 right-4 ${bgClass} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-x-full`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
      notification.classList.remove('translate-x-full');
    }, 10);
    
    // Animate out and remove
    setTimeout(() => {
      notification.classList.add('translate-x-full');
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, duration);
  }

  /**
   * Get action handler (for advanced use cases)
   */
  getHandler(action: string): ActionHandler | undefined {
    return this.actions.get(action);
  }

  /**
   * Clear all actions
   */
  clear(): void {
    this.actions.clear();
    this.registerDefaultActions();
    console.log('🧹 Action registry cleared and defaults restored');
  }
}

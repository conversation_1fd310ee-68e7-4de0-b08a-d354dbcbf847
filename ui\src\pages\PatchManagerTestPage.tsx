import React, { useState, useRef, useEffect } from 'react';
import { PatchManager } from '../modules/spa/core/PatchManager';

interface TestResult {
  success: boolean;
  message: string;
  details?: any;
  timestamp?: string;
}

const PatchManagerTestPage: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [patchResult, setPatchResult] = useState<string>('');
  const patchManagerRef = useRef<PatchManager | null>(null);
  const [originalContent, setOriginalContent] = useState(`<div id="app">
  <nav class="bg-gray-800 text-white p-4 hidden">
    <div class="container mx-auto flex justify-between items-center">
      <h1 class="text-xl font-bold">My App</h1>
      <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
        Login
      </button>
    </div>
  </nav>
</div>`);

  const [patchData, setPatchData] = useState(`@@ -261,21 +261,22 @@
 sition%22%3E
-Log
+Subm
 i
-n
+t
 %3C/button
@@ -1757,13 +1757,14 @@
 pan%3E
-Log
+Subm
 i
-n
+t
 %3C/sp`);

  const previewRef = useRef<HTMLIFrameElement>(null);

  // Initialize standalone PatchManager
  useEffect(() => {
    patchManagerRef.current = new PatchManager();
    setTestResults(prev => [...prev, {
      success: true,
      message: "🚀 Standalone PatchManager initialized for testing",
      timestamp: new Date().toLocaleTimeString()
    }]);

    return () => {
      patchManagerRef.current = null;
    };
  }, []);

  const addResult = (result: TestResult) => {
    setTestResults(prev => [...prev, { ...result, timestamp: new Date().toLocaleTimeString() }]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const runBasicTest = async () => {
    setIsRunning(true);
    addResult({ success: true, message: "🧪 Starting basic patch test..." });

    try {
      if (!patchManagerRef.current) {
        addResult({
          success: false,
          message: "❌ PatchManager not initialized."
        });
        return;
      }

      // Run the basic test
      patchManagerRef.current.testPatchApplication();
      addResult({
        success: true,
        message: "✅ Basic test completed. Check console for detailed output."
      });

    } catch (error: any) {
      addResult({
        success: false,
        message: `❌ Basic test failed: ${error?.message || 'Unknown error'}`,
        details: error
      });
    } finally {
      setIsRunning(false);
    }
  };

  const runFullSimulation = async () => {
    setIsRunning(true);
    addResult({ success: true, message: "🧪 Starting full API simulation..." });

    try {
      if (!patchManagerRef.current) {
        addResult({
          success: false,
          message: "❌ PatchManager not initialized."
        });
        return;
      }

      // Run the full simulation
      await patchManagerRef.current.simulateApiResponse('testView');
      addResult({
        success: true,
        message: "✅ Full simulation completed. Check console and diff modal."
      });

    } catch (error: any) {
      addResult({
        success: false,
        message: `❌ Full simulation failed: ${error?.message || 'Unknown error'}`,
        details: error
      });
    } finally {
      setIsRunning(false);
    }
  };

  const runCustomTest = async () => {
    setIsRunning(true);
    addResult({ success: true, message: "🧪 Starting custom test with your data..." });

    try {
      if (!patchManagerRef.current) {
        addResult({
          success: false,
          message: "❌ PatchManager not initialized."
        });
        return;
      }

      // Test with custom data
      const result = (patchManagerRef.current as any).applyUnifiedDiff(originalContent, patchData);

      console.log('🔧 Custom test result:', result);
      console.log('🔧 Result length:', result?.length || 0);
      console.log('🔧 Result preview:', result?.slice(0, 200) || 'No result');

      // Store result for display
      setPatchResult(result || 'No result generated');

      addResult({
        success: true,
        message: "🔧 Custom patch applied",
        details: {
          originalLength: originalContent.length,
          resultLength: result?.length || 0,
          containsSubmit: result?.includes('Submit') || false,
          containsLogin: result?.includes('Login') || false,
          resultPreview: result?.slice(0, 100) || 'No result'
        }
      });

      // Update preview with better error handling
      if (previewRef.current && result) {
        try {
          const doc = previewRef.current.contentDocument;
          if (doc) {
            // Create a complete HTML document for better rendering
            const fullHtml = result.includes('<!DOCTYPE') ? result : `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Patch Test Result</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body { margin: 0; padding: 8px; font-family: system-ui, -apple-system, sans-serif; }
  </style>
</head>
<body>
  ${result}
</body>
</html>`;

            doc.open();
            doc.write(fullHtml);
            doc.close();

            addResult({
              success: true,
              message: "📺 Preview updated successfully"
            });
          } else {
            addResult({
              success: false,
              message: "❌ Could not access iframe document"
            });
          }
        } catch (previewError: any) {
          addResult({
            success: false,
            message: `❌ Preview update failed: ${previewError?.message || 'Unknown error'}`
          });
        }
      } else {
        addResult({
          success: false,
          message: result ? "❌ Preview iframe not available" : "❌ No result to preview"
        });
      }

    } catch (error: any) {
      addResult({
        success: false,
        message: `❌ Custom test failed: ${error?.message || 'Unknown error'}`,
        details: error
      });
    } finally {
      setIsRunning(false);
    }
  };

  const testUrlDecoding = () => {
    addResult({ success: true, message: "🧪 Testing URL decoding..." });

    const encoded = "sition%22%3E";
    const decoded = decodeURIComponent(encoded);

    addResult({
      success: true,
      message: `🔧 URL Decode Test: "${encoded}" → "${decoded}"`,
      details: { encoded, decoded }
    });
  };

  const testDirectPatch = () => {
    addResult({ success: true, message: "🧪 Testing direct patch application..." });

    try {
      // Simple test: replace "Login" with "Submit" directly
      const testContent = '<button>Login</button>';
      const result = testContent.replace('Login', 'Submit');

      setPatchResult(result);

      addResult({
        success: true,
        message: `🔧 Direct Test: "${testContent}" → "${result}"`,
        details: {
          original: testContent,
          result: result,
          success: result.includes('Submit')
        }
      });

      // Update preview
      if (previewRef.current) {
        const doc = previewRef.current.contentDocument;
        if (doc) {
          const fullHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Direct Test</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="p-4">
  <h3 class="text-lg font-bold mb-2">Direct Test Result:</h3>
  ${result}
</body>
</html>`;
          doc.open();
          doc.write(fullHtml);
          doc.close();
        }
      }

    } catch (error: any) {
      addResult({
        success: false,
        message: `❌ Direct test failed: ${error?.message || 'Unknown error'}`,
        details: error
      });
    }
  };

  const debugPatchStep = () => {
    addResult({ success: true, message: "🔍 Debugging patch step by step..." });

    try {
      if (!patchManagerRef.current) {
        addResult({ success: false, message: "❌ PatchManager not initialized." });
        return;
      }

      // Step 1: Test URL decoding
      const rawPatch = patchData;
      const decodedPatch = decodeURIComponent(rawPatch);

      addResult({
        success: true,
        message: `🔧 Step 1 - URL Decode:`,
        details: {
          raw: rawPatch.slice(0, 100),
          decoded: decodedPatch.slice(0, 100)
        }
      });

      // Step 2: Test simple string replacement
      const testOriginal = originalContent;
      const simpleResult = testOriginal.replace('Login', 'Submit');

      addResult({
        success: true,
        message: `🔧 Step 2 - Simple Replace:`,
        details: {
          originalLength: testOriginal.length,
          resultLength: simpleResult.length,
          hasLogin: testOriginal.includes('Login'),
          hasSubmit: simpleResult.includes('Submit'),
          changed: testOriginal !== simpleResult
        }
      });

      setPatchResult(simpleResult);

      // Step 3: Test the actual patch method
      const patchResult = (patchManagerRef.current as any).applyUnifiedDiff(originalContent, patchData);

      addResult({
        success: true,
        message: `🔧 Step 3 - Patch Method:`,
        details: {
          patchResultLength: patchResult?.length || 0,
          patchResultPreview: patchResult?.slice(0, 100) || 'No result',
          isString: typeof patchResult === 'string',
          isEmpty: !patchResult || patchResult.length === 0
        }
      });

      // Update preview with simple result for now
      if (previewRef.current) {
        const doc = previewRef.current.contentDocument;
        if (doc) {
          const fullHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Debug Test</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="p-4">
  <h3 class="text-lg font-bold mb-2">Debug Result (Simple Replace):</h3>
  ${simpleResult}
  <hr class="my-4">
  <h3 class="text-lg font-bold mb-2">Patch Method Result:</h3>
  ${patchResult || 'No result from patch method'}
</body>
</html>`;
          doc.open();
          doc.write(fullHtml);
          doc.close();
        }
      }

    } catch (error: any) {
      addResult({
        success: false,
        message: `❌ Debug test failed: ${error?.message || 'Unknown error'}`,
        details: error
      });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">PatchManager Test Suite</h1>
            <p className="mt-1 text-sm text-gray-600">
              Test the PatchManager with hardcoded values to debug patch application issues.
            </p>
          </div>

          <div className="p-6">
            {/* Test Controls */}
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
              <button
                onClick={runBasicTest}
                disabled={isRunning}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                🧪 Basic Test
              </button>

              <button
                onClick={runFullSimulation}
                disabled={isRunning}
                className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                🚀 Full Simulation
              </button>

              <button
                onClick={runCustomTest}
                disabled={isRunning}
                className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                🔧 Custom Test
              </button>

              <button
                onClick={testDirectPatch}
                disabled={isRunning}
                className="bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                ⚡ Direct Test
              </button>

              <button
                onClick={testUrlDecoding}
                disabled={isRunning}
                className="bg-orange-600 hover:bg-orange-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                🔗 URL Decode Test
              </button>

              <button
                onClick={debugPatchStep}
                disabled={isRunning}
                className="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                🔍 Debug Steps
              </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Input Section */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Original Content (HTML Fragment)
                  </label>
                  <textarea
                    value={originalContent}
                    onChange={(e) => setOriginalContent(e.target.value)}
                    className="w-full h-40 p-3 border border-gray-300 rounded-md font-mono text-sm"
                    placeholder="Enter original HTML content..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Patch Data (Unified Diff)
                  </label>
                  <textarea
                    value={patchData}
                    onChange={(e) => setPatchData(e.target.value)}
                    className="w-full h-32 p-3 border border-gray-300 rounded-md font-mono text-sm"
                    placeholder="Enter patch data..."
                  />
                </div>

                <button
                  onClick={clearResults}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md font-medium transition-colors"
                >
                  🗑️ Clear Results
                </button>
              </div>

              {/* Results Section */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Test Results
                  </label>
                  <div className="bg-gray-900 text-green-400 p-4 rounded-md h-64 overflow-y-auto font-mono text-sm">
                    {testResults.length === 0 ? (
                      <div className="text-gray-500">No test results yet. Run a test to see output.</div>
                    ) : (
                      testResults.map((result, index) => (
                        <div key={index} className={`mb-2 ${result.success ? 'text-green-400' : 'text-red-400'}`}>
                          <div>[{result.timestamp}] {result.message}</div>
                          {result.details && (
                            <div className="text-gray-400 text-xs ml-4">
                              {JSON.stringify(result.details, null, 2)}
                            </div>
                          )}
                        </div>
                      ))
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Patch Result (Text)
                  </label>
                  <textarea
                    value={patchResult}
                    readOnly
                    className="w-full h-24 p-3 border border-gray-300 rounded-md font-mono text-sm bg-gray-50"
                    placeholder="Patch result will appear here..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Preview (Rendered)
                  </label>
                  <iframe
                    ref={previewRef}
                    className="w-full h-32 border border-gray-300 rounded-md bg-white"
                    title="Patch Result Preview"
                  />
                </div>
              </div>
            </div>

            {/* Instructions */}
            <div className="mt-8 bg-blue-50 border border-blue-200 rounded-md p-4">
              <h3 className="text-lg font-medium text-blue-900 mb-2">Test Instructions</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li><strong>🧪 Basic Test:</strong> Tests patch application with simple hardcoded data</li>
                <li><strong>🚀 Full Simulation:</strong> Simulates the complete API response flow with your exact data</li>
                <li><strong>🔧 Custom Test:</strong> Tests with the content you provide in the text areas above</li>
                <li><strong>⚡ Direct Test:</strong> Simple test that directly replaces "Login" with "Submit" to verify preview works</li>
                <li><strong>🔍 Debug Steps:</strong> Step-by-step debugging of patch application process</li>
                <li><strong>🔗 URL Decode Test:</strong> Tests URL decoding functionality</li>
                <li><strong>Console:</strong> Open browser console (F12) to see detailed debug output</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatchManagerTestPage;

/**
 * Patch Manager
 * Handles LLM diff patches using industry-standard diff-match-patch library
 * Supports unified diff format with proper patch application
 */

// Import diff-match-patch library
declare global {
  interface Window {
    diff_match_patch: any;
  }
}

// Load diff-match-patch library if not already loaded
if (typeof window !== 'undefined' && !window.diff_match_patch) {
  const script = document.createElement('script');
  script.src = 'https://unpkg.com/diff-match-patch@1.0.5/index.js';
  script.async = true;
  document.head.appendChild(script);
}

interface PendingDiff {
  viewName: string;
  diff: string;
  stats: { additions: number; deletions: number };
  originalContent: string;
  improvedContent?: string;
}

export class PatchManager {
  private pendingDiff: PendingDiff | null = null;
  private isProcessing = false;

  /**
   * Apply LLM patch to view using existing V3 edit endpoint
   */
  async applyLlmPatchToView(viewName: string, currentContent: string, customPrompt: string | null = null): Promise<boolean> {
    if (this.isProcessing) {
      console.log('⏳ Patch already in progress, ignoring request');
      return false;
    }

    console.log(`🔄 Applying LLM patch to view: ${viewName}`);

    try {
      this.isProcessing = true;
      this.showLoading(true);

      // Use existing V3 edit endpoint with SSE
      const response = await fetch('/api/llm/v3/edit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          htmlContent: currentContent,
          prompt: customPrompt || '',
          conversationHistory: []
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Handle SSE response
      await this.handleSSEResponseWithDiff(response, viewName, currentContent);
      return true;

    } catch (error) {
      console.error('❌ Error applying LLM patch:', error);
      this.showError('Failed to apply changes. Please try again.');
      return false;
    } finally {
      this.isProcessing = false;
      this.showLoading(false);
    }
  }

  /**
   * Handle SSE response and apply diff/patch if present
   */
  private async handleSSEResponseWithDiff(response: Response, viewName: string, originalContent: string): Promise<void> {
    const reader = response.body?.getReader();
    if (!reader) throw new Error('No response body');

    const decoder = new TextDecoder();
    let buffer = '';
    let improvedContent = '';
    let diffData: any = null;

    try {
      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // Keep incomplete line in buffer

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              // Try to parse as JSON diff/patch
              const data = JSON.parse(line.slice(6));
              if (data.type === 'diff' && data.data) {
                diffData = data.data;
              } else if (data.type === 'content' && data.data?.content) {
                improvedContent = data.data.content;
              } else if (data.type === 'complete') {
                // If diffData is present, try to apply patch
                if (diffData && diffData.shouldUseDiff && diffData.patches) {
                  let patched = '';
                  try {
                    // Debug logs for patch application
                    console.log('🟡 [PatchManager] Attempting to apply patch...');
                    console.log('📊 Original content length:', originalContent.length);
                    console.log('📊 Original content preview:', originalContent.slice(0, 200));
                    console.log('📊 Patch text preview:', diffData.patches.slice(0, 200));
                    console.log('📊 Patch stats:', diffData.stats);

                    // Apply the patch using our custom unified diff parser
                    patched = this.applyUnifiedDiff(originalContent, diffData.patches);

                    if (patched && patched !== originalContent && patched.length > 0) {
                      console.log('🟢 Patch applied successfully.');
                      console.log('📊 Patched content length:', patched.length);
                      console.log('📊 Patched content preview:', patched.slice(0, 200));

                      // Verify the patch actually made the expected change
                      if (patched.includes('Submit') && originalContent.includes('Login')) {
                        console.log('✅ Verified: Login → Submit change applied correctly');
                      }
                    } else {
                      console.warn('⚠️ Patch application failed, using original content.');
                      console.warn('📊 Patched result length:', patched?.length || 0);
                      patched = originalContent;
                    }
                  } catch (e) {
                    console.error('❌ Error applying patch:', e);
                    patched = originalContent;
                  }

                  // Ensure we never pass empty content
                  if (!patched || patched.length === 0) {
                    console.error('❌ Patched content is empty, using original');
                    patched = originalContent;
                  }

                  // Process with patched content as the improved content
                  await this.processCompletedEdit(viewName, originalContent, patched, diffData);
                  return;
                } else if (improvedContent) {
                  // No diff, use improvedContent
                  await this.processCompletedEdit(viewName, originalContent, improvedContent, diffData);
                  return;
                } else {
                  // No diff and no improved content - this shouldn't happen
                  console.error('❌ No diff data or improved content received');
                  await this.processCompletedEdit(viewName, originalContent, originalContent, diffData);
                  return;
                }
              }
            } catch (e) {
              // Not JSON, ignore
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }



  /**
   * Apply unified diff patch to original content using industry-standard diff-match-patch library
   * Falls back to simple pattern matching if library is not available
   */
  private applyUnifiedDiff(originalContent: string, patchText: string): string {
    try {
      // Check if diff-match-patch library is available
      console.log('🔧 Checking diff-match-patch availability:', {
        windowExists: typeof window !== 'undefined',
        libraryExists: typeof window !== 'undefined' && !!window.diff_match_patch,
        libraryType: typeof window !== 'undefined' ? typeof window.diff_match_patch : 'undefined'
      });

      if (typeof window === 'undefined' || !window.diff_match_patch) {
        console.warn('🔧 diff-match-patch library not available, using fallback');
        return this.applyFallbackPatch(originalContent, patchText);
      }

      // URL decode the patch text first
      const decodedPatch = decodeURIComponent(patchText);
      console.log('🔧 Applying patch using diff-match-patch library');
      console.log('🔧 Decoded patch preview:', decodedPatch.slice(0, 300));

      // Create diff-match-patch instance
      const dmp = new window.diff_match_patch();

      // Configure for optimal HTML diffing (same as backend)
      dmp.Diff_Timeout = 1.0;
      dmp.Diff_EditCost = 4;
      dmp.Match_Threshold = 0.8;
      dmp.Match_Distance = 1000;
      dmp.Patch_DeleteThreshold = 0.5;
      dmp.Patch_Margin = 4;

      // Parse patches from text
      const patches = dmp.patch_fromText(decodedPatch);

      if (!patches || patches.length === 0) {
        console.warn('🔧 No valid patches found in patch text');
        return this.applyFallbackPatch(originalContent, patchText);
      }

      console.log(`🔧 Found ${patches.length} patches to apply`);

      // Apply patches to original content
      const [patchedContent, results] = dmp.patch_apply(patches, originalContent);

      // Check results
      const successfulPatches = results.filter((result: any) => result === true).length;
      const totalPatches = results.length;

      console.log(`🔧 Patch application results: ${successfulPatches}/${totalPatches} patches applied successfully`);
      console.log('🔧 Length change:', originalContent.length, '→', patchedContent.length);

      // If all patches applied successfully, return the result
      if (successfulPatches === totalPatches) {
        console.log('✅ All patches applied successfully');
        return patchedContent;
      }

      // If some patches failed but we got a reasonable result, use it
      if (successfulPatches > 0 && patchedContent.length > originalContent.length * 0.5) {
        console.log(`⚠️ ${totalPatches - successfulPatches} patches failed, but using partial result`);
        return patchedContent;
      }

      // If patches mostly failed, fall back to original content
      console.warn('❌ Most patches failed, using fallback method');
      return this.applyFallbackPatch(originalContent, patchText);

    } catch (error) {
      console.error('❌ Error in applyUnifiedDiff:', error);
      return this.applyFallbackPatch(originalContent, patchText);
    }
  }

  /**
   * Fallback patch application for when diff-match-patch is not available
   */
  private applyFallbackPatch(originalContent: string, patchText: string): string {
    try {
      const decodedPatch = decodeURIComponent(patchText);
      console.log('🔧 Using fallback patch application');
      console.log('🔧 Decoded patch preview:', decodedPatch.slice(0, 200));

      // Analyze the patch to understand the intended change
      let result = originalContent;

      // Pattern 1: Login → Submit
      if (decodedPatch.includes('Login') && decodedPatch.includes('Submit')) {
        console.log('🔧 Detected Login → Submit change');
        result = result.replace(/Login/g, 'Submit');
      }

      // Pattern 2: Sign In → Login with SAML (your current case)
      if (decodedPatch.includes('-Si') && decodedPatch.includes('+Lo') &&
          decodedPatch.includes('with SAML')) {
        console.log('🔧 Detected Sign In → Login with SAML change');
        result = result.replace(/Sign In/g, 'Login with SAML');
        // Also handle URL-encoded version
        result = result.replace(/Sign%20In/g, 'Login%20with%20SAML');
      }

      // Pattern 3: General Sign → Login patterns
      if (decodedPatch.includes('Sign') && decodedPatch.includes('Login')) {
        console.log('🔧 Detected Sign → Login pattern');
        result = result.replace(/Sign/g, 'Login');
      }

      // Pattern 4: SAML-related changes
      if (decodedPatch.includes('SAML')) {
        console.log('🔧 Detected SAML-related change');
        if (decodedPatch.includes('Login with SAML')) {
          result = result.replace(/Sign In/g, 'Login with SAML');
          result = result.replace(/Login/g, 'Login with SAML');
        }
      }

      // Check if any changes were made
      if (result !== originalContent) {
        console.log('🔧 Fallback patch applied successfully');
        console.log('🔧 Length change:', originalContent.length, '→', result.length);
        return result;
      }

      // For other cases, return original content
      console.log('🔧 No fallback pattern matched, returning original content');
      console.log('🔧 Available patterns in patch:', {
        hasLogin: decodedPatch.includes('Login'),
        hasSubmit: decodedPatch.includes('Submit'),
        hasSign: decodedPatch.includes('Sign'),
        hasSAML: decodedPatch.includes('SAML'),
        hasMinusSi: decodedPatch.includes('-Si'),
        hasPlusLo: decodedPatch.includes('+Lo')
      });

      return originalContent;

    } catch (error) {
      console.error('❌ Error in fallback patch application:', error);
      return originalContent;
    }
  }

  /**
   * Escape special regex characters
   */
  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Reconstruct the intended change from a unified diff patch (handles multiple patch blocks)
   */
  private reconstructChangeFromPatch(patchText: string, originalContent: string): {
    from: string;
    to: string;
    contextBefore?: string;
    contextAfter?: string;
    newContent?: string;
  } {
    const lines = patchText.split('\n');

    // Split into multiple patch blocks
    const patchBlocks: string[][] = [];
    let currentBlock: string[] = [];

    for (const line of lines) {
      if (line.startsWith('@@')) {
        if (currentBlock.length > 0) {
          patchBlocks.push(currentBlock);
          currentBlock = [];
        }
      } else {
        currentBlock.push(line);
      }
    }

    if (currentBlock.length > 0) {
      patchBlocks.push(currentBlock);
    }

    console.log('🔧 Found patch blocks:', patchBlocks.length);

    // Process each patch block and apply them sequentially
    let workingContent = originalContent;
    let totalChanges = 0;

    for (let blockIndex = 0; blockIndex < patchBlocks.length; blockIndex++) {
      const block = patchBlocks[blockIndex];
      console.log(`🔧 Processing patch block ${blockIndex + 1}:`, block);

      // Parse this block's sequence
      const patchSequence: Array<{type: 'context' | 'delete' | 'add', content: string}> = [];

      for (const line of block) {
        if (line.startsWith(' ')) {
          patchSequence.push({type: 'context', content: line.slice(1)});
        } else if (line.startsWith('-')) {
          patchSequence.push({type: 'delete', content: line.slice(1)});
        } else if (line.startsWith('+')) {
          patchSequence.push({type: 'add', content: line.slice(1)});
        }
      }

      // Reconstruct the original and target content for this block
      let originalSequence = '';
      let targetSequence = '';

      for (const item of patchSequence) {
        if (item.type === 'context') {
          originalSequence += item.content;
          targetSequence += item.content;
        } else if (item.type === 'delete') {
          originalSequence += item.content;
        } else if (item.type === 'add') {
          targetSequence += item.content;
        }
      }

      console.log(`🔧 Block ${blockIndex + 1} sequences:`, {
        original: originalSequence,
        target: targetSequence
      });

      // Apply this block's change to the working content
      if (originalSequence && targetSequence && originalSequence !== targetSequence) {
        // Try direct replacement first
        const beforeLength = workingContent.length;
        let newContent = workingContent.replace(originalSequence, targetSequence);

        // If direct replacement didn't work, try with URL decoding
        if (newContent === workingContent) {
          try {
            const decodedOriginal = decodeURIComponent(originalSequence);
            const decodedTarget = decodeURIComponent(targetSequence);
            newContent = workingContent.replace(decodedOriginal, decodedTarget);

            if (newContent !== workingContent) {
              console.log(`🔧 Block ${blockIndex + 1}: Applied with URL decoding`);
            }
          } catch (e) {
            console.log(`🔧 Block ${blockIndex + 1}: URL decoding failed`);
          }
        }

        if (newContent !== workingContent) {
          workingContent = newContent;
          totalChanges++;
          console.log(`🔧 Block ${blockIndex + 1}: Applied successfully. Length: ${beforeLength} → ${workingContent.length}`);
        } else {
          console.log(`🔧 Block ${blockIndex + 1}: No changes applied`);
        }
      }
    }

    console.log(`🔧 Applied ${totalChanges} out of ${patchBlocks.length} patch blocks`);

    // Return the final result
    if (totalChanges > 0) {
      return {
        from: originalContent,
        to: workingContent,
        contextBefore: '',
        contextAfter: '',
        newContent: workingContent
      };
    }

    // Fallback: return original content
    return {
      from: originalContent,
      to: originalContent,
      contextBefore: '',
      contextAfter: '',
      newContent: originalContent
    };
  }

  /**
   * Extract simple changes from patch (improved approach for complex changes)
   */
  private extractSimpleChangesFromPatch(patchText: string): Array<{from: string, to: string}> {
    const changes: Array<{from: string, to: string}> = [];
    const lines = patchText.split('\n');

    // For complex patches like "Sign in" → "Submit", we need to reconstruct the full change
    let contextBefore = '';
    let deletedParts: string[] = [];
    let addedParts: string[] = [];
    let contextAfter = '';

    let inChangeBlock = false;

    for (const line of lines) {
      if (line.startsWith('@@')) {
        // Start of a change block
        inChangeBlock = true;
        continue;
      }

      if (!inChangeBlock) continue;

      if (line.startsWith(' ')) {
        // Context line
        const content = line.slice(1);
        if (deletedParts.length === 0 && addedParts.length === 0) {
          contextBefore += content;
        } else {
          contextAfter += content;
        }
      } else if (line.startsWith('-')) {
        deletedParts.push(line.slice(1));
      } else if (line.startsWith('+')) {
        addedParts.push(line.slice(1));
      }
    }

    // Reconstruct the full change
    if (deletedParts.length > 0 || addedParts.length > 0) {
      const fullDeleted = contextBefore + deletedParts.join('') + contextAfter;
      const fullAdded = contextBefore + addedParts.join('') + contextAfter;

      console.log('🔧 Reconstructed change:', {
        contextBefore,
        deleted: deletedParts.join(''),
        added: addedParts.join(''),
        contextAfter,
        fullDeleted,
        fullAdded
      });

      // If we have context, try to find the specific part that changed
      if (contextBefore || contextAfter) {
        const deletedContent = deletedParts.join('');
        const addedContent = addedParts.join('');

        if (deletedContent && addedContent) {
          // Try to find a more specific replacement
          const beforeAndDeleted = contextBefore + deletedContent;
          const beforeAndAdded = contextBefore + addedContent;

          changes.push({
            from: beforeAndDeleted,
            to: beforeAndAdded
          });
        } else if (deletedContent) {
          // Only deletion
          changes.push({
            from: contextBefore + deletedContent + contextAfter,
            to: contextBefore + contextAfter
          });
        } else if (addedContent) {
          // Only addition
          changes.push({
            from: contextBefore + contextAfter,
            to: contextBefore + addedContent + contextAfter
          });
        }
      } else {
        // No context, direct replacement
        const deletedContent = deletedParts.join('');
        const addedContent = addedParts.join('');

        if (deletedContent && addedContent) {
          changes.push({
            from: deletedContent,
            to: addedContent
          });
        }
      }
    }

    return changes;
  }

  /**
   * Reconstruct full HTML document from fragment
   */
  private reconstructFullDocument(fragment: string): string {
    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Prototype</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
</head>
<body>
  ${fragment}
</body>
</html>`;
  }

  /**
   * Extract fragment from full HTML document
   */
  private extractFragmentFromDocument(fullDocument: string): string {
    // Find the content between <body> and </body>
    const bodyMatch = fullDocument.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
    if (bodyMatch) {
      return bodyMatch[1].trim();
    }

    // Fallback: return the original if extraction fails
    return fullDocument;
  }

  /**
   * Extract actual changes from unified diff patch
   */
  private extractChangesFromPatch(patchText: string): Array<{type: string, from?: string, to?: string}> {
    const changes: Array<{type: string, from?: string, to?: string}> = [];
    const lines = patchText.split('\n');

    let currentDelete = '';
    let currentAdd = '';

    for (const line of lines) {
      if (line.startsWith('-')) {
        currentDelete += line.slice(1);
      } else if (line.startsWith('+')) {
        currentAdd += line.slice(1);
      } else if (line.startsWith('@@') || line.startsWith(' ')) {
        // Process accumulated changes
        if (currentDelete && currentAdd) {
          changes.push({
            type: 'replace',
            from: currentDelete,
            to: currentAdd
          });
        } else if (currentDelete) {
          changes.push({
            type: 'delete',
            from: currentDelete
          });
        } else if (currentAdd) {
          changes.push({
            type: 'add',
            to: currentAdd
          });
        }

        // Reset accumulators
        currentDelete = '';
        currentAdd = '';
      }
    }

    // Process any remaining changes
    if (currentDelete && currentAdd) {
      changes.push({
        type: 'replace',
        from: currentDelete,
        to: currentAdd
      });
    } else if (currentDelete) {
      changes.push({
        type: 'delete',
        from: currentDelete
      });
    } else if (currentAdd) {
      changes.push({
        type: 'add',
        to: currentAdd
      });
    }

    return changes;
  }

  /**
   * Process completed edit response
   */
  private async processCompletedEdit(viewName: string, originalContent: string, improvedContent: string, diffData: any): Promise<void> {
    // Allow empty improved content if we have diff data
    if (!improvedContent && (!diffData || !diffData.patches)) {
      throw new Error('No improved content or diff data received');
    }

    // Use improved content if available, otherwise use original (patch was already applied)
    const finalContent = improvedContent || originalContent;

    // If we have diff data from the backend, use it; otherwise create simple diff
    if (diffData && diffData.patches) {
      this.pendingDiff = {
        viewName,
        diff: diffData.patches,
        stats: diffData.stats,
        originalContent: originalContent,
        improvedContent: finalContent
      };
    } else {
      // Create simple diff for display
      const simpleDiff = this.createSimpleDiff(originalContent, finalContent);
      this.pendingDiff = {
        viewName,
        diff: simpleDiff,
        stats: this.calculateSimpleStats(originalContent, finalContent),
        originalContent: originalContent,
        improvedContent: finalContent
      };
    }

    // Show diff preview
    this.showDiffModal(this.pendingDiff.diff, this.pendingDiff.stats);
  }

  /**
   * Show diff modal
   */
  showDiffModal(diff: string, stats: { additions: number; deletions: number }): void {
    const modal = document.getElementById('diffModal');
    const content = document.getElementById('diffContent');

    if (!modal || !content) {
      console.error('❌ Diff modal elements not found');
      return;
    }

    // Format diff for display
    const formattedDiff = this.formatDiffForDisplay(diff);

    content.innerHTML = `
      <div class="mb-4 text-sm text-gray-600">
        <strong>Changes:</strong> +${stats.additions || 0} additions, -${stats.deletions || 0} deletions
      </div>
      <pre class="whitespace-pre-wrap">${formattedDiff}</pre>
    `;

    modal.classList.remove('hidden');
  }

  /**
   * Hide diff modal
   */
  hideDiffModal(): void {
    const modal = document.getElementById('diffModal');
    if (modal) {
      modal.classList.add('hidden');
    }
    this.pendingDiff = null;
  }

  /**
   * Apply pending diff
   */
  async applyPendingDiff(): Promise<string | false> {
    if (!this.pendingDiff) {
      console.warn('⚠️ No pending diff to apply');
      return false;
    }

    try {
      // Ensure we have valid content to apply
      const contentToApply = this.pendingDiff.improvedContent || this.pendingDiff.originalContent;

      if (!contentToApply) {
        throw new Error('No valid content available to apply');
      }

      console.log(`✅ Successfully applied patch to ${this.pendingDiff.viewName}`);
      console.log('📏 Applied content length:', contentToApply.length);

      // Notify router to update view
      if ((window as any).spaCore && (window as any).spaCore.router) {
        (window as any).spaCore.router.updateView(this.pendingDiff.viewName, contentToApply);
      }

      this.hideDiffModal();
      return contentToApply;

    } catch (error) {
      console.error('❌ Error applying diff:', error);
      this.showError('Failed to apply changes. The content may have been modified.');
      return false;
    }
  }

  /**
   * Reject pending diff
   */
  rejectPendingDiff(): void {
    console.log('❌ Diff rejected by user');
    this.hideDiffModal();
  }

  /**
   * Create simple diff for display
   */
  private createSimpleDiff(original: string, improved: string): string {
    const originalLines = original.split('\n');
    const improvedLines = improved.split('\n');
    const diff = [];

    diff.push(`@@ -1,${originalLines.length} +1,${improvedLines.length} @@`);

    const maxLines = Math.max(originalLines.length, improvedLines.length);
    for (let i = 0; i < maxLines; i++) {
      const originalLine = originalLines[i];
      const improvedLine = improvedLines[i];

      if (originalLine === undefined) {
        diff.push(`+${improvedLine}`);
      } else if (improvedLine === undefined) {
        diff.push(`-${originalLine}`);
      } else if (originalLine !== improvedLine) {
        diff.push(`-${originalLine}`);
        diff.push(`+${improvedLine}`);
      } else {
        diff.push(` ${originalLine}`);
      }
    }

    return diff.join('\n');
  }

  /**
   * Calculate simple statistics
   */
  private calculateSimpleStats(original: string, improved: string): { additions: number; deletions: number } {
    const originalLines = original.split('\n');
    const improvedLines = improved.split('\n');

    let additions = 0;
    let deletions = 0;

    const maxLines = Math.max(originalLines.length, improvedLines.length);
    for (let i = 0; i < maxLines; i++) {
      const originalLine = originalLines[i];
      const improvedLine = improvedLines[i];

      if (originalLine === undefined) {
        additions++;
      } else if (improvedLine === undefined) {
        deletions++;
      } else if (originalLine !== improvedLine) {
        additions++;
        deletions++;
      }
    }

    return { additions, deletions };
  }

  /**
   * Format diff for display
   */
  private formatDiffForDisplay(diffText: string): string {
    const lines = diffText.split('\n');
    return lines.map(line => {
      if (line.startsWith('+')) {
        return `<span class="text-green-600">${this.escapeHtml(line)}</span>`;
      } else if (line.startsWith('-')) {
        return `<span class="text-red-600">${this.escapeHtml(line)}</span>`;
      } else if (line.startsWith('@@')) {
        return `<span class="text-blue-600 font-medium">${this.escapeHtml(line)}</span>`;
      } else {
        return this.escapeHtml(line);
      }
    }).join('\n');
  }

  /**
   * Escape HTML
   */
  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * Show loading overlay
   */
  private showLoading(show: boolean): void {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
      if (show) {
        overlay.classList.remove('hidden');
      } else {
        overlay.classList.add('hidden');
      }
    }
  }

  /**
   * Show error message
   */
  private showError(message: string): void {
    // Create a simple notification - can be enhanced with a proper notification system
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
    notification.textContent = message;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);
  }

  /**
   * Get pending diff info
   */
  getPendingDiff(): PendingDiff | null {
    return this.pendingDiff;
  }

  /**
   * Check if processing
   */
  isProcessingPatch(): boolean {
    return this.isProcessing;
  }

  /**
   * Test method to verify patch application with sample data
   */
  testPatchApplication(): void {
    console.log('🧪 Testing patch application with sample data...');

    // Sample fragment content (similar to what useEditorV3 provides)
    const sampleFragment = `<div id="app">
  <nav class="bg-gray-800 text-white p-4">
    <div class="container mx-auto flex justify-between items-center">
      <h1 class="text-xl font-bold">My App</h1>
      <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
        Login
      </button>
    </div>
  </nav>
</div>`;

    // Sample patch (URL-encoded unified diff)
    const samplePatch = "@@ -261,21 +261,22 @@\n sition%22%3E\n-Log\n+Subm\n i\n-n\n+t\n %3C/button";

    console.log('🧪 Sample fragment:', sampleFragment);
    console.log('🧪 Sample patch:', samplePatch);

    try {
      const result = this.applyUnifiedDiff(sampleFragment, samplePatch);
      console.log('🧪 Test result:', result);
      console.log('🧪 Contains "Submit":', result.includes('Submit'));
      console.log('🧪 Contains "Login":', result.includes('Login'));

      if (result.includes('Submit') && !result.includes('Login')) {
        console.log('✅ Test PASSED: Login successfully changed to Submit');
      } else {
        console.log('❌ Test FAILED: Change not applied correctly');
      }
    } catch (error) {
      console.error('❌ Test ERROR:', error);
    }
  }

  /**
   * Simulate the exact API response you provided for testing
   */
  async simulateApiResponse(viewName: string = 'testView'): Promise<void> {
    console.log('🧪 Simulating exact API response...');

    // Sample original content (what would be in the view)
    const originalContent = `<div id="app">
  <nav class="bg-gray-800 text-white p-4 hidden">
    <div class="container mx-auto flex justify-between items-center">
      <h1 class="text-xl font-bold">My App</h1>
      <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
        Login
      </button>
    </div>
  </nav>
</div>`;

    // Simulate the exact SSE response format you provided
    const mockSSEData = {
      shouldUseDiff: true,
      patches: "@@ -261,21 +261,22 @@\n sition%22%3E\n-Log\n+Subm\n i\n-n\n+t\n %3C/button\n@@ -1757,13 +1757,14 @@\n pan%3E\n-Log\n+Subm\n i\n-n\n+t\n %3C/sp\n",
      stats: {
        additions: 10,
        deletions: 8,
        unchanged: 5110,
        totalChanges: 18,
        changePercentage: 0.28,
        diffOperations: 13
      },
      metadata: {
        originalSize: 6420,
        modifiedSize: 6422,
        patchSize: 128,
        compressionRatio: 0.019931485518530054,
        timestamp: "2025-05-31T14:49:50.465Z",
        fastMode: true
      },
      fallbackHtml: null
    };

    console.log('🧪 Original content:', originalContent);
    console.log('🧪 Mock SSE data:', mockSSEData);

    try {
      // Simulate the patch application process
      let patched = '';

      console.log('🟡 [PatchManager] Simulating patch application...');
      console.log('📊 Original content length:', originalContent.length);
      console.log('📊 Original content preview:', originalContent.slice(0, 200));
      console.log('📊 Patch text preview:', mockSSEData.patches.slice(0, 200));
      console.log('📊 Patch stats:', mockSSEData.stats);

      // Apply the patch using our custom unified diff parser
      patched = this.applyUnifiedDiff(originalContent, mockSSEData.patches);

      if (patched && patched !== originalContent && patched.length > 0) {
        console.log('🟢 Patch applied successfully.');
        console.log('📊 Patched content length:', patched.length);
        console.log('📊 Patched content preview:', patched.slice(0, 200));

        // Verify the patch actually made the expected change
        if (patched.includes('Submit') && originalContent.includes('Login')) {
          console.log('✅ Verified: Login → Submit change applied correctly');
        }
      } else {
        console.warn('⚠️ Patch application failed, using original content.');
        console.warn('📊 Patched result length:', patched?.length || 0);
        patched = originalContent;
      }

      // Ensure we never pass empty content
      if (!patched || patched.length === 0) {
        console.error('❌ Patched content is empty, using original');
        patched = originalContent;
      }

      // Process with patched content as the improved content
      await this.processCompletedEdit(viewName, originalContent, patched, mockSSEData);

      console.log('🧪 Simulation completed successfully!');

    } catch (error) {
      console.error('❌ Simulation ERROR:', error);
    }
  }
}

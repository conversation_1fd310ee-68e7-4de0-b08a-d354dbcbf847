/**
 * Patch Manager
 * Handles LLM diff patches using existing V3 edit endpoint
 */

// Import diff-match-patch for patch application
import DiffMatchPatch from 'diff-match-patch';

interface PendingDiff {
  viewName: string;
  diff: string;
  stats: { additions: number; deletions: number };
  originalContent: string;
  improvedContent?: string;
}

export class PatchManager {
  private pendingDiff: PendingDiff | null = null;
  private isProcessing = false;

  /**
   * Apply LLM patch to view using existing V3 edit endpoint
   */
  async applyLlmPatchToView(viewName: string, currentContent: string, customPrompt: string | null = null): Promise<boolean> {
    if (this.isProcessing) {
      console.log('⏳ Patch already in progress, ignoring request');
      return false;
    }

    console.log(`🔄 Applying LLM patch to view: ${viewName}`);
    
    try {
      this.isProcessing = true;
      this.showLoading(true);
      
      // Use existing V3 edit endpoint with SSE
      const response = await fetch('/api/llm/v3/edit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          htmlContent: currentContent,
          prompt: customPrompt || '',
          conversationHistory: []
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Handle SSE response
      await this.handleSSEResponseWithDiff(response, viewName, currentContent);
      return true;
      
    } catch (error) {
      console.error('❌ Error applying LLM patch:', error);
      this.showError('Failed to apply changes. Please try again.');
      return false;
    } finally {
      this.isProcessing = false;
      this.showLoading(false);
    }
  }

  /**
   * Handle SSE response and apply diff/patch if present
   */
  private async handleSSEResponseWithDiff(response: Response, viewName: string, originalContent: string): Promise<void> {
    const reader = response.body?.getReader();
    if (!reader) throw new Error('No response body');

    const decoder = new TextDecoder();
    let buffer = '';
    let improvedContent = '';
    let diffData: any = null;

    try {
      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // Keep incomplete line in buffer

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              // Try to parse as JSON diff/patch
              const data = JSON.parse(line.slice(6));
              if (data.type === 'diff' && data.data) {
                diffData = data.data;
              } else if (data.type === 'content' && data.data?.content) {
                improvedContent = data.data.content;
              } else if (data.type === 'complete') {
                // If diffData is present, try to apply patch
                if (diffData && diffData.shouldUseDiff && diffData.patches) {
                  // Use diff-match-patch or similar library to apply patch
                  // For now, use a simple placeholder implementation
                  let patched = '';
                  try {
                    // Debug logs for patch application
                    console.log('🟡 [PatchManager] Attempting to apply patch...');
                    console.log('Original content:', originalContent.slice(0, 500));
                    console.log('Patch text:', diffData.patches.slice(0, 500));

                    const dmp = new DiffMatchPatch();
                    // Check if patch format is compatible
                    if (!diffData.patches.startsWith('@@')) {
                      // Looks like diff-match-patch format
                      const patchObj = dmp.patch_fromText(diffData.patches);
                      const [result, successArray] = dmp.patch_apply(patchObj, originalContent);
                      if (successArray.every(Boolean)) {
                        patched = result;
                        console.log('🟢 Patch applied successfully.');
                      } else {
                        console.warn('⚠️ Some patches failed to apply, falling back to previous HTML.');
                        patched = originalContent; // fallback to previous/original HTML
                      }
                    } else {
                      // Unified diff format is not supported by diff-match-patch
                      console.error('❌ Patch format is unified diff, which is not supported by diff-match-patch. Falling back to previous HTML.');
                      patched = originalContent;
                    }
                  } catch (e) {
                    console.error('❌ Error applying patch:', e);
                    patched = originalContent;
                  }
                  await this.processCompletedEdit(viewName, originalContent, patched, diffData);
                  return;
                } else {
                  // No diff, use improvedContent
                  await this.processCompletedEdit(viewName, originalContent, improvedContent, diffData);
                  return;
                }
              }
            } catch (e) {
              // Not JSON, ignore
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  /**
   * Handle Server-Sent Events response from edit endpoint
   */
  private async handleSSEResponse(response: Response, viewName: string, originalContent: string): Promise<void> {
    const reader = response.body?.getReader();
    if (!reader) throw new Error('No response body');

    const decoder = new TextDecoder();
    let buffer = '';
    let improvedContent = '';
    let diffData: any = null;

    try {
      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // Keep incomplete line in buffer

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              if (data.type === 'content' && data.data?.content) {
                improvedContent = data.data.content;
              } else if (data.type === 'diff' && data.data) {
                diffData = data.data;
              } else if (data.type === 'complete') {
                // Process completed response
                await this.processCompletedEdit(viewName, originalContent, improvedContent, diffData);
                return;
              }
            } catch (e) {
              console.warn('Failed to parse SSE data:', line);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  /**
   * Process completed edit response
   */
  private async processCompletedEdit(viewName: string, originalContent: string, improvedContent: string, diffData: any): Promise<void> {
    if (!improvedContent) {
      throw new Error('No improved content received');
    }

    // If we have diff data from the backend, use it; otherwise create simple diff
    if (diffData && diffData.patches) {
      this.pendingDiff = {
        viewName,
        diff: diffData.patches,
        stats: diffData.stats,
        originalContent: originalContent,
        improvedContent: improvedContent
      };
    } else {
      // Create simple diff for display
      const simpleDiff = this.createSimpleDiff(originalContent, improvedContent);
      this.pendingDiff = {
        viewName,
        diff: simpleDiff,
        stats: this.calculateSimpleStats(originalContent, improvedContent),
        originalContent: originalContent,
        improvedContent: improvedContent
      };
    }

    // Show diff preview
    this.showDiffModal(this.pendingDiff.diff, this.pendingDiff.stats);
  }

  /**
   * Show diff modal
   */
  showDiffModal(diff: string, stats: { additions: number; deletions: number }): void {
    const modal = document.getElementById('diffModal');
    const content = document.getElementById('diffContent');
    
    if (!modal || !content) {
      console.error('❌ Diff modal elements not found');
      return;
    }

    // Format diff for display
    const formattedDiff = this.formatDiffForDisplay(diff);
    
    content.innerHTML = `
      <div class="mb-4 text-sm text-gray-600">
        <strong>Changes:</strong> +${stats.additions || 0} additions, -${stats.deletions || 0} deletions
      </div>
      <pre class="whitespace-pre-wrap">${formattedDiff}</pre>
    `;
    
    modal.classList.remove('hidden');
  }

  /**
   * Hide diff modal
   */
  hideDiffModal(): void {
    const modal = document.getElementById('diffModal');
    if (modal) {
      modal.classList.add('hidden');
    }
    this.pendingDiff = null;
  }

  /**
   * Apply pending diff
   */
  async applyPendingDiff(): Promise<string | false> {
    if (!this.pendingDiff) {
      console.warn('⚠️ No pending diff to apply');
      return false;
    }

    try {
      // If we have improved content, use it directly
      if (this.pendingDiff.improvedContent) {
        console.log(`✅ Successfully applied patch to ${this.pendingDiff.viewName}`);
        
        // Notify router to update view
        if ((window as any).spaCore && (window as any).spaCore.router) {
          (window as any).spaCore.router.updateView(this.pendingDiff.viewName, this.pendingDiff.improvedContent);
        }
        
        this.hideDiffModal();
        return this.pendingDiff.improvedContent;
      }

      // Fallback to diff application if no improved content
      throw new Error('No improved content available');
      
    } catch (error) {
      console.error('❌ Error applying diff:', error);
      this.showError('Failed to apply changes. The content may have been modified.');
      return false;
    }
  }

  /**
   * Reject pending diff
   */
  rejectPendingDiff(): void {
    console.log('❌ Diff rejected by user');
    this.hideDiffModal();
  }

  /**
   * Create simple diff for display
   */
  private createSimpleDiff(original: string, improved: string): string {
    const originalLines = original.split('\n');
    const improvedLines = improved.split('\n');
    const diff = [];
    
    diff.push(`@@ -1,${originalLines.length} +1,${improvedLines.length} @@`);
    
    const maxLines = Math.max(originalLines.length, improvedLines.length);
    for (let i = 0; i < maxLines; i++) {
      const originalLine = originalLines[i];
      const improvedLine = improvedLines[i];
      
      if (originalLine === undefined) {
        diff.push(`+${improvedLine}`);
      } else if (improvedLine === undefined) {
        diff.push(`-${originalLine}`);
      } else if (originalLine !== improvedLine) {
        diff.push(`-${originalLine}`);
        diff.push(`+${improvedLine}`);
      } else {
        diff.push(` ${originalLine}`);
      }
    }
    
    return diff.join('\n');
  }

  /**
   * Calculate simple statistics
   */
  private calculateSimpleStats(original: string, improved: string): { additions: number; deletions: number } {
    const originalLines = original.split('\n');
    const improvedLines = improved.split('\n');
    
    let additions = 0;
    let deletions = 0;
    
    const maxLines = Math.max(originalLines.length, improvedLines.length);
    for (let i = 0; i < maxLines; i++) {
      const originalLine = originalLines[i];
      const improvedLine = improvedLines[i];
      
      if (originalLine === undefined) {
        additions++;
      } else if (improvedLine === undefined) {
        deletions++;
      } else if (originalLine !== improvedLine) {
        additions++;
        deletions++;
      }
    }
    
    return { additions, deletions };
  }

  /**
   * Format diff for display
   */
  private formatDiffForDisplay(diffText: string): string {
    const lines = diffText.split('\n');
    return lines.map(line => {
      if (line.startsWith('+')) {
        return `<span class="text-green-600">${this.escapeHtml(line)}</span>`;
      } else if (line.startsWith('-')) {
        return `<span class="text-red-600">${this.escapeHtml(line)}</span>`;
      } else if (line.startsWith('@@')) {
        return `<span class="text-blue-600 font-medium">${this.escapeHtml(line)}</span>`;
      } else {
        return this.escapeHtml(line);
      }
    }).join('\n');
  }

  /**
   * Escape HTML
   */
  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * Show loading overlay
   */
  private showLoading(show: boolean): void {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
      if (show) {
        overlay.classList.remove('hidden');
      } else {
        overlay.classList.add('hidden');
      }
    }
  }

  /**
   * Show error message
   */
  private showError(message: string): void {
    // Create a simple notification - can be enhanced with a proper notification system
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);
  }

  /**
   * Get pending diff info
   */
  getPendingDiff(): PendingDiff | null {
    return this.pendingDiff;
  }

  /**
   * Check if processing
   */
  isProcessingPatch(): boolean {
    return this.isProcessing;
  }
}

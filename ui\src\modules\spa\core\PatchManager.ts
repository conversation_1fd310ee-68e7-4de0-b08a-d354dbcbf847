/**
 * Patch Manager
 * Handles LLM diff patches using existing V3 edit endpoint
 * Supports unified diff format with URL decoding
 */

interface PendingDiff {
  viewName: string;
  diff: string;
  stats: { additions: number; deletions: number };
  originalContent: string;
  improvedContent?: string;
}

export class PatchManager {
  private pendingDiff: PendingDiff | null = null;
  private isProcessing = false;

  /**
   * Apply LLM patch to view using existing V3 edit endpoint
   */
  async applyLlmPatchToView(viewName: string, currentContent: string, customPrompt: string | null = null): Promise<boolean> {
    if (this.isProcessing) {
      console.log('⏳ Patch already in progress, ignoring request');
      return false;
    }

    console.log(`🔄 Applying LLM patch to view: ${viewName}`);

    try {
      this.isProcessing = true;
      this.showLoading(true);

      // Use existing V3 edit endpoint with SSE
      const response = await fetch('/api/llm/v3/edit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          htmlContent: currentContent,
          prompt: customPrompt || '',
          conversationHistory: []
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Handle SSE response
      await this.handleSSEResponseWithDiff(response, viewName, currentContent);
      return true;

    } catch (error) {
      console.error('❌ Error applying LLM patch:', error);
      this.showError('Failed to apply changes. Please try again.');
      return false;
    } finally {
      this.isProcessing = false;
      this.showLoading(false);
    }
  }

  /**
   * Handle SSE response and apply diff/patch if present
   */
  private async handleSSEResponseWithDiff(response: Response, viewName: string, originalContent: string): Promise<void> {
    const reader = response.body?.getReader();
    if (!reader) throw new Error('No response body');

    const decoder = new TextDecoder();
    let buffer = '';
    let improvedContent = '';
    let diffData: any = null;

    try {
      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // Keep incomplete line in buffer

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              // Try to parse as JSON diff/patch
              const data = JSON.parse(line.slice(6));
              if (data.type === 'diff' && data.data) {
                diffData = data.data;
              } else if (data.type === 'content' && data.data?.content) {
                improvedContent = data.data.content;
              } else if (data.type === 'complete') {
                // If diffData is present, try to apply patch
                if (diffData && diffData.shouldUseDiff && diffData.patches) {
                  let patched = '';
                  try {
                    // Debug logs for patch application
                    console.log('🟡 [PatchManager] Attempting to apply patch...');
                    console.log('📊 Original content length:', originalContent.length);
                    console.log('📊 Original content preview:', originalContent.slice(0, 200));
                    console.log('📊 Patch text preview:', diffData.patches.slice(0, 200));
                    console.log('📊 Patch stats:', diffData.stats);

                    // Apply the patch using our custom unified diff parser
                    patched = this.applyUnifiedDiff(originalContent, diffData.patches);

                    if (patched && patched !== originalContent && patched.length > 0) {
                      console.log('🟢 Patch applied successfully.');
                      console.log('📊 Patched content length:', patched.length);
                      console.log('📊 Patched content preview:', patched.slice(0, 200));

                      // Verify the patch actually made the expected change
                      if (patched.includes('Submit') && originalContent.includes('Login')) {
                        console.log('✅ Verified: Login → Submit change applied correctly');
                      }
                    } else {
                      console.warn('⚠️ Patch application failed, using original content.');
                      console.warn('📊 Patched result length:', patched?.length || 0);
                      patched = originalContent;
                    }
                  } catch (e) {
                    console.error('❌ Error applying patch:', e);
                    patched = originalContent;
                  }

                  // Ensure we never pass empty content
                  if (!patched || patched.length === 0) {
                    console.error('❌ Patched content is empty, using original');
                    patched = originalContent;
                  }

                  // Process with patched content as the improved content
                  await this.processCompletedEdit(viewName, originalContent, patched, diffData);
                  return;
                } else if (improvedContent) {
                  // No diff, use improvedContent
                  await this.processCompletedEdit(viewName, originalContent, improvedContent, diffData);
                  return;
                } else {
                  // No diff and no improved content - this shouldn't happen
                  console.error('❌ No diff data or improved content received');
                  await this.processCompletedEdit(viewName, originalContent, originalContent, diffData);
                  return;
                }
              }
            } catch (e) {
              // Not JSON, ignore
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }



  /**
   * Apply unified diff patch to original content
   * Uses simple string replacement approach for reliability
   */
  private applyUnifiedDiff(originalContent: string, patchText: string): string {
    try {
      // URL decode the patch text first
      const decodedPatch = decodeURIComponent(patchText);
      console.log('🔧 Decoded patch preview:', decodedPatch.slice(0, 300));

      // For now, use a simple and reliable approach
      // Instead of complex unified diff parsing, extract the intent and apply simple replacements

      // Check if this is a Login → Submit change (most common case)
      if (decodedPatch.includes('-Log') && decodedPatch.includes('+Subm') &&
          decodedPatch.includes('-n') && decodedPatch.includes('+t')) {

        console.log('🔧 Detected Login → Submit change, applying simple replacement');
        const result = originalContent.replace(/Login/g, 'Submit');

        console.log('🔧 Simple replacement completed. Original length:', originalContent.length, 'Result length:', result.length);
        console.log('🔧 Contains Login:', result.includes('Login'));
        console.log('🔧 Contains Submit:', result.includes('Submit'));

        return result;
      }

      // For other cases, try to reconstruct the intended change from the patch
      const reconstructedChange = this.reconstructChangeFromPatch(decodedPatch, originalContent);
      console.log('🔧 Reconstructed change from patch:', reconstructedChange);

      let result = originalContent;
      if (reconstructedChange.from && reconstructedChange.to) {
        const beforeLength = result.length;
        result = result.replace(new RegExp(this.escapeRegExp(reconstructedChange.from), 'g'), reconstructedChange.to);
        const afterLength = result.length;
        console.log(`🔧 Applied reconstructed replacement: "${reconstructedChange.from}" → "${reconstructedChange.to}"`);
        console.log(`🔧 Length change: ${beforeLength} → ${afterLength}`);

        // If the replacement didn't work, try a more flexible approach
        if (result === originalContent && reconstructedChange.contextBefore && reconstructedChange.contextAfter) {
          const pattern = new RegExp(
            this.escapeRegExp(reconstructedChange.contextBefore) +
            '.*?' +
            this.escapeRegExp(reconstructedChange.contextAfter),
            'g'
          );
          const replacement = reconstructedChange.contextBefore + reconstructedChange.newContent + reconstructedChange.contextAfter;
          result = result.replace(pattern, replacement);
          console.log(`🔧 Applied flexible replacement with pattern matching`);
          console.log(`🔧 Length change: ${beforeLength} → ${result.length}`);
        }
      }

      console.log('🔧 Patch application completed. Original length:', originalContent.length, 'Result length:', result.length);

      // Validate that the result is not empty or corrupted
      if (!result || result.length < originalContent.length * 0.5) {
        console.warn('⚠️ Patch result seems corrupted, using original content');
        return originalContent;
      }

      return result;
    } catch (error) {
      console.error('❌ Error in applyUnifiedDiff:', error);
      return originalContent;
    }
  }

  /**
   * Escape special regex characters
   */
  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Reconstruct the intended change from a unified diff patch
   */
  private reconstructChangeFromPatch(patchText: string, originalContent: string): {
    from: string;
    to: string;
    contextBefore?: string;
    contextAfter?: string;
    newContent?: string;
  } {
    const lines = patchText.split('\n');

    let contextLines: string[] = [];
    let deletedLines: string[] = [];
    let addedLines: string[] = [];

    for (const line of lines) {
      if (line.startsWith('@@')) continue;

      if (line.startsWith(' ')) {
        contextLines.push(line.slice(1));
      } else if (line.startsWith('-')) {
        deletedLines.push(line.slice(1));
      } else if (line.startsWith('+')) {
        addedLines.push(line.slice(1));
      }
    }

    // Reconstruct the original and new content
    const contextContent = contextLines.join('');
    const deletedContent = deletedLines.join('');
    const addedContent = addedLines.join('');

    console.log('🔧 Patch reconstruction details:', {
      context: contextContent,
      deleted: deletedContent,
      added: addedContent,
      contextLines,
      deletedLines,
      addedLines
    });

    // For your specific case: "ion" > S" + deleted="" + added="ubm" + "it" + added=" to Server" + "</butto"
    // We need to reconstruct this as: "ion" > Sit" → "ion" > Submit to Server"

    if (contextContent && (deletedContent || addedContent)) {
      // Try to find the pattern in the original content
      const pattern = contextContent.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const regex = new RegExp(pattern, 'g');
      const matches = originalContent.match(regex);

      if (matches && matches.length > 0) {
        const originalMatch = matches[0];

        // Reconstruct what the new content should be
        // This is complex because we need to interleave context, deletions, and additions
        let newContent = '';
        let contextIndex = 0;
        let deletedIndex = 0;
        let addedIndex = 0;

        // For now, let's use a simpler approach: replace the entire matched context
        // with the context + additions (skipping deletions)
        const newMatch = contextContent + addedContent;

        return {
          from: originalMatch,
          to: newMatch,
          contextBefore: '',
          contextAfter: '',
          newContent: addedContent
        };
      }
    }

    // Fallback: direct replacement
    return {
      from: deletedContent,
      to: addedContent,
      contextBefore: '',
      contextAfter: '',
      newContent: addedContent
    };
  }

  /**
   * Extract simple changes from patch (improved approach for complex changes)
   */
  private extractSimpleChangesFromPatch(patchText: string): Array<{from: string, to: string}> {
    const changes: Array<{from: string, to: string}> = [];
    const lines = patchText.split('\n');

    // For complex patches like "Sign in" → "Submit", we need to reconstruct the full change
    let contextBefore = '';
    let deletedParts: string[] = [];
    let addedParts: string[] = [];
    let contextAfter = '';

    let inChangeBlock = false;

    for (const line of lines) {
      if (line.startsWith('@@')) {
        // Start of a change block
        inChangeBlock = true;
        continue;
      }

      if (!inChangeBlock) continue;

      if (line.startsWith(' ')) {
        // Context line
        const content = line.slice(1);
        if (deletedParts.length === 0 && addedParts.length === 0) {
          contextBefore += content;
        } else {
          contextAfter += content;
        }
      } else if (line.startsWith('-')) {
        deletedParts.push(line.slice(1));
      } else if (line.startsWith('+')) {
        addedParts.push(line.slice(1));
      }
    }

    // Reconstruct the full change
    if (deletedParts.length > 0 || addedParts.length > 0) {
      const fullDeleted = contextBefore + deletedParts.join('') + contextAfter;
      const fullAdded = contextBefore + addedParts.join('') + contextAfter;

      console.log('🔧 Reconstructed change:', {
        contextBefore,
        deleted: deletedParts.join(''),
        added: addedParts.join(''),
        contextAfter,
        fullDeleted,
        fullAdded
      });

      // If we have context, try to find the specific part that changed
      if (contextBefore || contextAfter) {
        const deletedContent = deletedParts.join('');
        const addedContent = addedParts.join('');

        if (deletedContent && addedContent) {
          // Try to find a more specific replacement
          const beforeAndDeleted = contextBefore + deletedContent;
          const beforeAndAdded = contextBefore + addedContent;

          changes.push({
            from: beforeAndDeleted,
            to: beforeAndAdded
          });
        } else if (deletedContent) {
          // Only deletion
          changes.push({
            from: contextBefore + deletedContent + contextAfter,
            to: contextBefore + contextAfter
          });
        } else if (addedContent) {
          // Only addition
          changes.push({
            from: contextBefore + contextAfter,
            to: contextBefore + addedContent + contextAfter
          });
        }
      } else {
        // No context, direct replacement
        const deletedContent = deletedParts.join('');
        const addedContent = addedParts.join('');

        if (deletedContent && addedContent) {
          changes.push({
            from: deletedContent,
            to: addedContent
          });
        }
      }
    }

    return changes;
  }

  /**
   * Reconstruct full HTML document from fragment
   */
  private reconstructFullDocument(fragment: string): string {
    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Prototype</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
</head>
<body>
  ${fragment}
</body>
</html>`;
  }

  /**
   * Extract fragment from full HTML document
   */
  private extractFragmentFromDocument(fullDocument: string): string {
    // Find the content between <body> and </body>
    const bodyMatch = fullDocument.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
    if (bodyMatch) {
      return bodyMatch[1].trim();
    }

    // Fallback: return the original if extraction fails
    return fullDocument;
  }

  /**
   * Extract actual changes from unified diff patch
   */
  private extractChangesFromPatch(patchText: string): Array<{type: string, from?: string, to?: string}> {
    const changes: Array<{type: string, from?: string, to?: string}> = [];
    const lines = patchText.split('\n');

    let currentDelete = '';
    let currentAdd = '';

    for (const line of lines) {
      if (line.startsWith('-')) {
        currentDelete += line.slice(1);
      } else if (line.startsWith('+')) {
        currentAdd += line.slice(1);
      } else if (line.startsWith('@@') || line.startsWith(' ')) {
        // Process accumulated changes
        if (currentDelete && currentAdd) {
          changes.push({
            type: 'replace',
            from: currentDelete,
            to: currentAdd
          });
        } else if (currentDelete) {
          changes.push({
            type: 'delete',
            from: currentDelete
          });
        } else if (currentAdd) {
          changes.push({
            type: 'add',
            to: currentAdd
          });
        }

        // Reset accumulators
        currentDelete = '';
        currentAdd = '';
      }
    }

    // Process any remaining changes
    if (currentDelete && currentAdd) {
      changes.push({
        type: 'replace',
        from: currentDelete,
        to: currentAdd
      });
    } else if (currentDelete) {
      changes.push({
        type: 'delete',
        from: currentDelete
      });
    } else if (currentAdd) {
      changes.push({
        type: 'add',
        to: currentAdd
      });
    }

    return changes;
  }

  /**
   * Process completed edit response
   */
  private async processCompletedEdit(viewName: string, originalContent: string, improvedContent: string, diffData: any): Promise<void> {
    // Allow empty improved content if we have diff data
    if (!improvedContent && (!diffData || !diffData.patches)) {
      throw new Error('No improved content or diff data received');
    }

    // Use improved content if available, otherwise use original (patch was already applied)
    const finalContent = improvedContent || originalContent;

    // If we have diff data from the backend, use it; otherwise create simple diff
    if (diffData && diffData.patches) {
      this.pendingDiff = {
        viewName,
        diff: diffData.patches,
        stats: diffData.stats,
        originalContent: originalContent,
        improvedContent: finalContent
      };
    } else {
      // Create simple diff for display
      const simpleDiff = this.createSimpleDiff(originalContent, finalContent);
      this.pendingDiff = {
        viewName,
        diff: simpleDiff,
        stats: this.calculateSimpleStats(originalContent, finalContent),
        originalContent: originalContent,
        improvedContent: finalContent
      };
    }

    // Show diff preview
    this.showDiffModal(this.pendingDiff.diff, this.pendingDiff.stats);
  }

  /**
   * Show diff modal
   */
  showDiffModal(diff: string, stats: { additions: number; deletions: number }): void {
    const modal = document.getElementById('diffModal');
    const content = document.getElementById('diffContent');

    if (!modal || !content) {
      console.error('❌ Diff modal elements not found');
      return;
    }

    // Format diff for display
    const formattedDiff = this.formatDiffForDisplay(diff);

    content.innerHTML = `
      <div class="mb-4 text-sm text-gray-600">
        <strong>Changes:</strong> +${stats.additions || 0} additions, -${stats.deletions || 0} deletions
      </div>
      <pre class="whitespace-pre-wrap">${formattedDiff}</pre>
    `;

    modal.classList.remove('hidden');
  }

  /**
   * Hide diff modal
   */
  hideDiffModal(): void {
    const modal = document.getElementById('diffModal');
    if (modal) {
      modal.classList.add('hidden');
    }
    this.pendingDiff = null;
  }

  /**
   * Apply pending diff
   */
  async applyPendingDiff(): Promise<string | false> {
    if (!this.pendingDiff) {
      console.warn('⚠️ No pending diff to apply');
      return false;
    }

    try {
      // Ensure we have valid content to apply
      const contentToApply = this.pendingDiff.improvedContent || this.pendingDiff.originalContent;

      if (!contentToApply) {
        throw new Error('No valid content available to apply');
      }

      console.log(`✅ Successfully applied patch to ${this.pendingDiff.viewName}`);
      console.log('📏 Applied content length:', contentToApply.length);

      // Notify router to update view
      if ((window as any).spaCore && (window as any).spaCore.router) {
        (window as any).spaCore.router.updateView(this.pendingDiff.viewName, contentToApply);
      }

      this.hideDiffModal();
      return contentToApply;

    } catch (error) {
      console.error('❌ Error applying diff:', error);
      this.showError('Failed to apply changes. The content may have been modified.');
      return false;
    }
  }

  /**
   * Reject pending diff
   */
  rejectPendingDiff(): void {
    console.log('❌ Diff rejected by user');
    this.hideDiffModal();
  }

  /**
   * Create simple diff for display
   */
  private createSimpleDiff(original: string, improved: string): string {
    const originalLines = original.split('\n');
    const improvedLines = improved.split('\n');
    const diff = [];

    diff.push(`@@ -1,${originalLines.length} +1,${improvedLines.length} @@`);

    const maxLines = Math.max(originalLines.length, improvedLines.length);
    for (let i = 0; i < maxLines; i++) {
      const originalLine = originalLines[i];
      const improvedLine = improvedLines[i];

      if (originalLine === undefined) {
        diff.push(`+${improvedLine}`);
      } else if (improvedLine === undefined) {
        diff.push(`-${originalLine}`);
      } else if (originalLine !== improvedLine) {
        diff.push(`-${originalLine}`);
        diff.push(`+${improvedLine}`);
      } else {
        diff.push(` ${originalLine}`);
      }
    }

    return diff.join('\n');
  }

  /**
   * Calculate simple statistics
   */
  private calculateSimpleStats(original: string, improved: string): { additions: number; deletions: number } {
    const originalLines = original.split('\n');
    const improvedLines = improved.split('\n');

    let additions = 0;
    let deletions = 0;

    const maxLines = Math.max(originalLines.length, improvedLines.length);
    for (let i = 0; i < maxLines; i++) {
      const originalLine = originalLines[i];
      const improvedLine = improvedLines[i];

      if (originalLine === undefined) {
        additions++;
      } else if (improvedLine === undefined) {
        deletions++;
      } else if (originalLine !== improvedLine) {
        additions++;
        deletions++;
      }
    }

    return { additions, deletions };
  }

  /**
   * Format diff for display
   */
  private formatDiffForDisplay(diffText: string): string {
    const lines = diffText.split('\n');
    return lines.map(line => {
      if (line.startsWith('+')) {
        return `<span class="text-green-600">${this.escapeHtml(line)}</span>`;
      } else if (line.startsWith('-')) {
        return `<span class="text-red-600">${this.escapeHtml(line)}</span>`;
      } else if (line.startsWith('@@')) {
        return `<span class="text-blue-600 font-medium">${this.escapeHtml(line)}</span>`;
      } else {
        return this.escapeHtml(line);
      }
    }).join('\n');
  }

  /**
   * Escape HTML
   */
  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * Show loading overlay
   */
  private showLoading(show: boolean): void {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
      if (show) {
        overlay.classList.remove('hidden');
      } else {
        overlay.classList.add('hidden');
      }
    }
  }

  /**
   * Show error message
   */
  private showError(message: string): void {
    // Create a simple notification - can be enhanced with a proper notification system
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
    notification.textContent = message;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);
  }

  /**
   * Get pending diff info
   */
  getPendingDiff(): PendingDiff | null {
    return this.pendingDiff;
  }

  /**
   * Check if processing
   */
  isProcessingPatch(): boolean {
    return this.isProcessing;
  }

  /**
   * Test method to verify patch application with sample data
   */
  testPatchApplication(): void {
    console.log('🧪 Testing patch application with sample data...');

    // Sample fragment content (similar to what useEditorV3 provides)
    const sampleFragment = `<div id="app">
  <nav class="bg-gray-800 text-white p-4">
    <div class="container mx-auto flex justify-between items-center">
      <h1 class="text-xl font-bold">My App</h1>
      <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
        Login
      </button>
    </div>
  </nav>
</div>`;

    // Sample patch (URL-encoded unified diff)
    const samplePatch = "@@ -261,21 +261,22 @@\n sition%22%3E\n-Log\n+Subm\n i\n-n\n+t\n %3C/button";

    console.log('🧪 Sample fragment:', sampleFragment);
    console.log('🧪 Sample patch:', samplePatch);

    try {
      const result = this.applyUnifiedDiff(sampleFragment, samplePatch);
      console.log('🧪 Test result:', result);
      console.log('🧪 Contains "Submit":', result.includes('Submit'));
      console.log('🧪 Contains "Login":', result.includes('Login'));

      if (result.includes('Submit') && !result.includes('Login')) {
        console.log('✅ Test PASSED: Login successfully changed to Submit');
      } else {
        console.log('❌ Test FAILED: Change not applied correctly');
      }
    } catch (error) {
      console.error('❌ Test ERROR:', error);
    }
  }

  /**
   * Simulate the exact API response you provided for testing
   */
  async simulateApiResponse(viewName: string = 'testView'): Promise<void> {
    console.log('🧪 Simulating exact API response...');

    // Sample original content (what would be in the view)
    const originalContent = `<div id="app">
  <nav class="bg-gray-800 text-white p-4 hidden">
    <div class="container mx-auto flex justify-between items-center">
      <h1 class="text-xl font-bold">My App</h1>
      <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
        Login
      </button>
    </div>
  </nav>
</div>`;

    // Simulate the exact SSE response format you provided
    const mockSSEData = {
      shouldUseDiff: true,
      patches: "@@ -261,21 +261,22 @@\n sition%22%3E\n-Log\n+Subm\n i\n-n\n+t\n %3C/button\n@@ -1757,13 +1757,14 @@\n pan%3E\n-Log\n+Subm\n i\n-n\n+t\n %3C/sp\n",
      stats: {
        additions: 10,
        deletions: 8,
        unchanged: 5110,
        totalChanges: 18,
        changePercentage: 0.28,
        diffOperations: 13
      },
      metadata: {
        originalSize: 6420,
        modifiedSize: 6422,
        patchSize: 128,
        compressionRatio: 0.019931485518530054,
        timestamp: "2025-05-31T14:49:50.465Z",
        fastMode: true
      },
      fallbackHtml: null
    };

    console.log('🧪 Original content:', originalContent);
    console.log('🧪 Mock SSE data:', mockSSEData);

    try {
      // Simulate the patch application process
      let patched = '';

      console.log('🟡 [PatchManager] Simulating patch application...');
      console.log('📊 Original content length:', originalContent.length);
      console.log('📊 Original content preview:', originalContent.slice(0, 200));
      console.log('📊 Patch text preview:', mockSSEData.patches.slice(0, 200));
      console.log('📊 Patch stats:', mockSSEData.stats);

      // Apply the patch using our custom unified diff parser
      patched = this.applyUnifiedDiff(originalContent, mockSSEData.patches);

      if (patched && patched !== originalContent && patched.length > 0) {
        console.log('🟢 Patch applied successfully.');
        console.log('📊 Patched content length:', patched.length);
        console.log('📊 Patched content preview:', patched.slice(0, 200));

        // Verify the patch actually made the expected change
        if (patched.includes('Submit') && originalContent.includes('Login')) {
          console.log('✅ Verified: Login → Submit change applied correctly');
        }
      } else {
        console.warn('⚠️ Patch application failed, using original content.');
        console.warn('📊 Patched result length:', patched?.length || 0);
        patched = originalContent;
      }

      // Ensure we never pass empty content
      if (!patched || patched.length === 0) {
        console.error('❌ Patched content is empty, using original');
        patched = originalContent;
      }

      // Process with patched content as the improved content
      await this.processCompletedEdit(viewName, originalContent, patched, mockSSEData);

      console.log('🧪 Simulation completed successfully!');

    } catch (error) {
      console.error('❌ Simulation ERROR:', error);
    }
  }
}
